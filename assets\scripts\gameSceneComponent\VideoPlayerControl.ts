const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';

@ccclass
export default class VideoPlayerControl extends cc.Component {
  // 遮罩层和视频元素
  private maskElement: HTMLElement = null;
  private videoContainer: HTMLElement = null;
  private xgPlayer: any = null; // xgplayer实例

  // 按钮元素
  private completeBtn: HTMLButtonElement = null;
  private cancelBtn: HTMLButtonElement = null;

  // 保存原始宽高
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  onLoad() {
    // 动态加载xgplayer资源，然后启动视频
    this.loadXGPlayerResources();
  }

  /**
   * 动态加载xgplayer资源
   */
  private loadXGPlayerResources() {
    // 检查是否已经加载过
    if (typeof window['Player'] !== 'undefined') {
      cc.log('XGPlayer已加载，直接启动视频');
      this.insertVideoWithMask();
      return;
    }

    cc.log('开始动态加载XGPlayer资源...');

    // 加载CSS
    this.loadCSS(
      'https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.css'
    )
      .then(() => {
        cc.log('XGPlayer CSS加载完成');
        // 加载JS
        return this.loadScript(
          'https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.js'
        );
      })
      .then(() => {
        cc.log('XGPlayer JS加载完成');
        // 启动视频播放器
        this.insertVideoWithMask();
      })
      .catch(error => {
        cc.error('加载XGPlayer CDN资源失败:', error);
        cc.log('尝试加载本地资源...');
        this.loadLocalXGPlayerResources();
      });
  }

  /**
   * 动态加载CSS文件
   */
  private loadCSS(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过该CSS
      const existingLink = document.querySelector(`link[href="${url}"]`);
      if (existingLink) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = url;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load CSS: ${url}`));
      document.head.appendChild(link);
    });
  }

  /**
   * 动态加载JS文件
   */
  private loadScript(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过该脚本
      const existingScript = document.querySelector(`script[src="${url}"]`);
      if (existingScript) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = url;
      script.charset = 'utf-8';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
      document.head.appendChild(script);
    });
  }

  /**
   * 加载本地xgplayer资源（备选方案）
   */
  private loadLocalXGPlayerResources() {
    // 尝试加载本地资源
    this.loadCSS('./libs/xgplayer/index.min.css')
      .then(() => {
        cc.log('本地XGPlayer CSS加载完成');
        return this.loadScript('./libs/xgplayer/index.min.js');
      })
      .then(() => {
        cc.log('本地XGPlayer JS加载完成');
        this.insertVideoWithMask();
      })
      .catch(error => {
        cc.error('加载本地XGPlayer资源也失败:', error);
        cc.error('无法加载XGPlayer，将使用原生video标签');
      });
  }

  /**
   * 观看视频按钮点击事件
   * 调用游戏场景管理器的增加时长方法
   */
  watchVideoClick() {
    // 获取游戏场景管理器的引用
    const gameSceneManager = this.node.parent.getComponent('gameSceneManager');
    if (gameSceneManager && gameSceneManager.addExtraTimeAfterVideo) {
      // 调用增加时长方法，默认增加30秒
      gameSceneManager.addExtraTimeAfterVideo(30);
      cc.log('观看视频按钮被点击，尝试增加30秒游戏时间');
    } else {
      cc.error('无法获取游戏场景管理器引用或方法不存在');
    }
  }
  /**
   * 手动插入video和mask - 对外调用的主方法
   */
  insertVideoWithMask() {
    // 使用xgplayer需要用到资源
    // <link rel="stylesheet" href="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.css">
    // <script src="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.js" charset="utf-8"></script>

    // 创建遮罩层
    this.createMask();

    // 创建video元素
    this.createVideo();

    // xgplayer已在初始化时设置好样式和事件监听
  }

  /**
   * 创建遮罩层
   */
  private createMask() {
    this.maskElement = document.createElement('div');
    this.maskElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9998;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    `;
    document.body.appendChild(this.maskElement);
  }

  /**
   * 创建video元素 - 使用xgplayer
   */
  private createVideo() {
    // 创建视频容器 - 在父元素上统一设置横屏旋转
    this.videoContainer = document.createElement('div');
    this.videoContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 30px;
      width: 100%;
      height: 100%;
      justify-content: center;
      transform: rotate(90deg);
      transform-origin: center center;
    `;

    // 创建xgplayer容器
    const playerContainer = document.createElement('div');
    playerContainer.id = 'xgplayer-container-' + Date.now();
    playerContainer.style.cssText = `
      width: 400px;
      height: 225px;
    `;

    this.videoContainer.appendChild(playerContainer);

    this.sc
    // 初始化xgplayer
    this.initXGPlayer(playerContainer.id);

    // 创建按钮容器
    this.createButtons(this.videoContainer);

    // 将视频容器添加到遮罩层
    this.maskElement.appendChild(this.videoContainer);
  }

  /**
   * 初始化xgplayer播放器
   */
  private initXGPlayer(containerId: string) {
    // 检查xgplayer是否已加载
    if (typeof window['Player'] === 'undefined') {
      cc.error('XGPlayer未加载，请检查静态资源引入');
      return;
    }

    try {
      this.xgPlayer = new window['Player']({
        id: containerId,
        url: 'https://vod.jgrm.net/550126f54ec741ce35ecdb067bbb3537_raw.mp4',
        width: 400,
        height: 225,
        autoplay: true,
        playsinline: true,
        controls: false, // 隐藏默认控制条
        poster: '', // 可以设置封面图
        // 移动端优化配置
        'webkit-playsinline': true,
        'x5-playsinline': true,
        'x5-video-player-type': 'h5-page',
      });

      // 设置播放器事件监听
      this.setupXGPlayerEvents();
    } catch (error) {
      cc.error('初始化XGPlayer失败:', error);
    }
  }

  /**
   * 设置xgplayer事件监听
   */
  private setupXGPlayerEvents() {
    if (!this.xgPlayer) return;

    // 播放完成事件
    this.xgPlayer.on('ended', () => {
      cc.log('XGPlayer: 视频播放完成');
      this.onVideoCompleted();
    });

    // 播放开始事件
    this.xgPlayer.on('play', () => {
      cc.log('XGPlayer: 视频开始播放');
    });

    // 暂停事件
    this.xgPlayer.on('pause', () => {
      cc.log('XGPlayer: 视频暂停');
    });

    // 加载完成事件
    this.xgPlayer.on('loadeddata', () => {
      cc.log('XGPlayer: 视频数据加载完成');
    });

    // 错误事件
    this.xgPlayer.on('error', error => {
      cc.error('XGPlayer: 播放错误', error);
    });
  }

  /**
   * 创建按钮 - 显示在video下方（父元素已设置旋转）
   */
  private createButtons(container: HTMLElement) {
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: flex;
      gap: 20px;
    `;

    // 创建"观看完成"按钮 - 初始隐藏
    this.completeBtn = document.createElement('button');
    this.completeBtn.textContent = '观看完成';
    this.completeBtn.style.cssText = `
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: none;
    `;
    this.completeBtn.onclick = () => this.onCompleteButtonClick();

    // 创建"放弃"按钮 - 初始显示
    this.cancelBtn = document.createElement('button');
    this.cancelBtn.textContent = '放弃';
    this.cancelBtn.style.cssText = `
      padding: 10px 20px;
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: block;
    `;
    this.cancelBtn.onclick = () => this.onCancelButtonClick();

    buttonContainer.appendChild(this.completeBtn);
    buttonContainer.appendChild(this.cancelBtn);
    container.appendChild(buttonContainer);
  }

  /**
   * 视频播放完成回调
   */
  private onVideoCompleted() {
    cc.log('视频播放完成，显示观看完成按钮，隐藏放弃按钮');

    // 显示"观看完成"按钮
    if (this.completeBtn) {
      this.completeBtn.style.display = 'block';
    }

    // 隐藏"放弃"按钮
    if (this.cancelBtn) {
      this.cancelBtn.style.display = 'none';
    }
  }

  /**
   * 完成按钮点击事件
   */
  private onCompleteButtonClick() {
    cc.log('观看完成按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 放弃按钮点击事件
   */
  private onCancelButtonClick() {
    cc.log('放弃按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 关闭video和mask
   */
  closeVideoAndMask() {
    // 销毁xgplayer实例
    if (this.xgPlayer) {
      try {
        this.xgPlayer.destroy();
        cc.log('XGPlayer实例已销毁');
      } catch (error) {
        cc.error('销毁XGPlayer实例失败:', error);
      }
      this.xgPlayer = null;
    }

    // 移除遮罩层（包含video和按钮）
    if (this.maskElement) {
      this.maskElement.remove();
      this.maskElement = null;
    }

    // 重置容器和按钮引用
    this.videoContainer = null;
    this.completeBtn = null;
    this.cancelBtn = null;

    cc.log('video和mask已关闭');
  }

  /**
   * 开始播放视频 - 对外调用的方法
   */
  startVideo() {
    // 如果xgplayer已加载，直接播放
    if (typeof window['Player'] !== 'undefined') {
      this.insertVideoWithMask();
    } else {
      // 否则先加载资源
      this.loadXGPlayerResources();
    }
  }

  onDestroy() {
    // 清理资源，确保video和mask被正确移除
    this.closeVideoAndMask();
  }
}
