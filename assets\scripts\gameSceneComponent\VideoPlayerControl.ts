const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';

@ccclass
export default class VideoPlayerControl extends cc.Component {
  @property(cc.Node)
  VideoPlayer: cc.Node = null;

  @property(cc.Button)
  completeButton: cc.Button = null;

  // 遮罩层和视频元素
  private maskElement: HTMLElement = null;
  private videoElement: HTMLVideoElement = null;

  // 保存原始宽高
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  onLoad() {
    // 记录原始宽高
    this.originalWidth = this.VideoPlayer.width;
    this.originalHeight = this.VideoPlayer.height;

    // 初始化完成按钮状态
    if (this.completeButton) {
      this.completeButton.node.active = false;
      this.completeButton.node.on('click', this.onCompleteButtonClick, this);
    }
    this.insertVideoWithMask();
  }

  /**
   * 手动插入video和mask - 对外调用的主方法
   */
  insertVideoWithMask() {
    // 创建遮罩层
    this.createMask();

    // 创建video元素
    this.createVideo();

    // 设置video样式为横屏居中
    this.setupVideoStyle();

    // 监听video播放完成事件
    this.setupVideoEvents();
  }

  /**
   * 创建遮罩层
   */
  private createMask() {
    this.maskElement = document.createElement('div');
    this.maskElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9998;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    `;
    document.body.appendChild(this.maskElement);
  }

  /**
   * 创建video元素
   */
  private createVideo() {
    // 创建视频容器
    const videoContainer = document.createElement('div');
    videoContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    `;

    // 创建视频元素
    let videoStr = `
      <video class="cocosvideos" preload="auto" webkit-playsinline="" autoplay playsinline="true" webkit-playsinline="true" x5-playsinline="true" x5-video-player-type="h5-page" tabindex="2" mediatype="video">
        <source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.mp4">
        <source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.ogg">
        <source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.ogv">
        <source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.webm">
      </video>
    `;

    videoContainer.insertAdjacentHTML('beforeend', videoStr);
    this.videoElement = videoContainer.querySelector(
      'video'
    ) as HTMLVideoElement;

    // 创建按钮容器
    this.createButtons(videoContainer);

    // 将视频容器添加到遮罩层
    this.maskElement.appendChild(videoContainer);
  }

  /**
   * 创建按钮
   */
  private createButtons(container: HTMLElement) {
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: flex;
      gap: 20px;
      margin-top: 20px;
    `;

    // 创建"观看完成"按钮
    const completeBtn = document.createElement('button');
    completeBtn.textContent = '观看完成';
    completeBtn.style.cssText = `
      padding: 12px 24px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      min-width: 120px;
    `;
    completeBtn.onclick = () => this.onCompleteButtonClick();

    // 创建"放弃"按钮
    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = '放弃';
    cancelBtn.style.cssText = `
      padding: 12px 24px;
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      min-width: 120px;
    `;
    cancelBtn.onclick = () => this.onCancelButtonClick();

    buttonContainer.appendChild(completeBtn);
    buttonContainer.appendChild(cancelBtn);
    container.appendChild(buttonContainer);
  }

  /**
   * 设置video横屏居中样式 - 16:9比例
   */
  private setupVideoStyle() {
    if (!this.videoElement) return;

    // 去掉 Cocos 自动加的矩阵
    this.videoElement.style.transform = 'none';

    // 设置基础样式
    this.videoElement.style.position = 'relative';
    this.videoElement.style.transformOrigin = 'center center';
    this.videoElement.style.zIndex = '9999';

    // 设置16:9比例尺寸 (横屏状态下)
    const videoWidth = 480; // 横屏宽度
    const videoHeight = 270; // 16:9比例对应的高度 (480 * 9 / 16 = 270)

    this.videoElement.style.width = `${videoWidth}px`;
    this.videoElement.style.height = `${videoHeight}px`;

    // 横屏旋转 - 旋转90度
    this.videoElement.style.transform = 'rotate(90deg)';
  }

  /**
   * 设置video事件监听
   */
  private setupVideoEvents() {
    if (!this.videoElement) return;

    // 监听视频播放完成事件
    this.videoElement.addEventListener('ended', () => {
      cc.log('视频播放完成');
      this.onVideoCompleted();
    });

    // 监听视频加载完成事件
    this.videoElement.addEventListener('loadeddata', () => {
      cc.log('视频数据加载完成');
    });

    // 监听视频播放开始事件
    this.videoElement.addEventListener('play', () => {
      cc.log('视频开始播放');
    });

    // 监听视频暂停事件
    this.videoElement.addEventListener('pause', () => {
      cc.log('视频暂停');
    });
  }

  /**
   * 视频播放完成回调
   */
  private onVideoCompleted() {
    cc.log('视频播放完成');
    // 视频播放完成后，按钮已经在视频下方显示，无需额外操作
    // 用户可以选择"观看完成"或"放弃"
  }

  /**
   * 完成按钮点击事件
   */
  private onCompleteButtonClick() {
    cc.log('观看完成按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 放弃按钮点击事件
   */
  private onCancelButtonClick() {
    cc.log('放弃按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 关闭video和mask
   */
  closeVideoAndMask() {
    // 移除video元素
    if (this.videoElement) {
      this.videoElement.pause();
      this.videoElement = null;
    }

    // 移除遮罩层（包含video和按钮）
    if (this.maskElement) {
      this.maskElement.remove();
      this.maskElement = null;
    }

    cc.log('video和mask已关闭');
  }

  onVideoPlayerEvent(player: any, eventType: any, customEventData: any) {
    cc.log('videoplayer', player);
    cc.log('eventType', eventType);

    if (eventType === cc.VideoPlayer.EventType.READY_TO_PLAY) {
      // 若视频准备好了，这个事件并不保障会在所有平台或浏览器中被触发，它依赖于平台实现，请不要依赖于这个事件做视频播放的控制。
      cc.log('视频准备播放了');

      // this.scheduleOnce(() => {
      //   player.play();
      // }, 500);
      // setTimeout(() => {
      //   let videos = document.getElementsByTagName('video');
      //   if (videos.length > 0) {
      //     let v = videos[0];
      //     // 假设你能拿到 video 元素
      //     let video = document.querySelector('video');

      //     // 让它保持原本大小
      //     video.style.width = '500px';
      //     video.style.height = '280px';
      //     // 居中
      //     video.style.position = 'absolute';
      //     video.style.top = '50%';
      //     video.style.left = '50%';
      //     video.style.bottom = 'initial';

      //     // 先 rotate 再 translate 才能保持中心点正确
      //     video.style.transformOrigin = 'center center';
      //     // 去掉 Cocos 自动加的矩阵
      //     video.style.transform = 'none';
      //     // 自己设定横屏旋转 & 居中
      //     video.style.transform = 'rotate(-90deg) translate(-50%, -50%)';

      //     // this.rotateVideo90deg(v);
      //   }
      // }, 5000);
    } else if (eventType === cc.VideoPlayer.EventType.PLAYING) {
      // 若视频正在播放,do something...
      cc.log('视频正在播放');
    } else if (eventType === cc.VideoPlayer.EventType.PAUSED) {
      // 若视频暂停, do something...
      cc.log('视频暂停了');
    } else if (eventType === cc.VideoPlayer.EventType.STOPPED) {
      // 若视频停止, do something...
      cc.log('视频停止了');
    } else if (eventType === cc.VideoPlayer.EventType.COMPLETED) {
      // 若播放结束,do something...
      cc.log('视频的元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.META_LOADED) {
      // 若视频的元信息已加载完成，你可以调用 getDuration 来获取视频总时长1
      cc.log('视频元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.CLICKED) {
      // 若点击了视频, do something...
      // 勾选了StayOnBottom后这个不能用
      cc.log('点击了视频');
    }
    //这里 videoplayer 是一个 VideoPlayer 组件对象实例
    // 这里的 eventType === cc.VideoPlayer.EventType enum 里面的值
    //这里的 customEventData 参数就等于你之前设置的 "foobar"
  }
  /**
   * 开始播放视频 - 对外调用的方法
   */
  startVideo() {
    this.insertVideoWithMask();
  }

  rotateVideo90deg(video: HTMLVideoElement) {
    // let v = video;
    // v.style.position = 'absolute';
    // v.style.zIndex = '9999'; // 确保在前面（按需调整）1
    // v.style.left = '50%';
    // v.style.top = '50%';
    // v.style.transform = 'translate(-50%, -50%) !important';
    // v.style.transformOrigin = 'center center !important';
    // v.style.width = '100vw'; // 占满屏幕宽
    // v.style.height = 'auto'; // 按比例自适应高
    // v.style.maxHeight = '100vh'; // 防止高度超出屏幕3
    // 1. 获取当前 transform 矩阵
    const style = window.getComputedStyle(video);
    const matrix = new DOMMatrixReadOnly(style.transform);
    cc.log('matrix', matrix);
    // 2. 元素的大小（包含缩放）
    const rect = video.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    cc.log('rect', rect);
    // 3. 转换：平移到中心 → 旋转 90° → 平移回去
    const rotated = new DOMMatrix()
      .translate(centerX, centerY)
      .rotate(90) // 旋转90度
      .translate(-centerX, -centerY)
      .multiply(matrix);
    // 4. 应用新的矩阵
    video.style.transform = `matrix(${rotated.a}, ${rotated.b}, ${rotated.c}, ${rotated.d}, ${rotated.e}, ${rotated.f})`;
  }
  onDestroy() {
    // 清理资源，确保video和mask被正确移除
    this.closeVideoAndMask();

    // 移除完成按钮事件监听
    if (this.completeButton) {
      this.completeButton.node.off('click', this.onCompleteButtonClick, this);
    }
  }
}
