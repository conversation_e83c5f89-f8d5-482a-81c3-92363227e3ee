const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import { AppConstants } from '../utils/constants';
import Request from '../apis/api';

@ccclass
export default class VideoPlayerControl extends cc.Component {
  @property(cc.Node)
  musicNote: cc.Node = null;

  // 遮罩层和视频元素
  private maskElement: HTMLElement = null;
  private videoContainer: HTMLElement = null;
  private xgPlayer: any = null; // xgplayer实例

  // 按钮元素
  private completeBtn: HTMLButtonElement = null;
  private cancelBtn: HTMLButtonElement = null;

  // 屏幕尺寸变化监听
  private resizeHandler: () => void = null;

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  onEnable() {
    // 动态加载xgplayer资源，然后启动视频
    this.loadXGPlayerResources();

    // 监听屏幕尺寸变化
    this.addResizeListener();
  }

  /**
   * 动态加载xgplayer资源
   */
  private loadXGPlayerResources() {
    // 检查是否已经加载过
    if (typeof window['Player'] !== 'undefined') {
      cc.log('XGPlayer已加载，直接启动视频');
      this.insertVideoWithMask();
      return;
    }

    cc.log('开始动态加载XGPlayer资源...');

    // 加载CSS
    this.loadCSS(
      'https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.css'
    )
      .then(() => {
        cc.log('XGPlayer CSS加载完成');
        // 加载JS
        return this.loadScript(
          'https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.js'
        );
      })
      .then(() => {
        cc.log('XGPlayer JS加载完成');
        // 启动视频播放器
        this.insertVideoWithMask();
      })
      .catch(error => {
        cc.error('加载XGPlayer CDN资源失败:', error);
        cc.log('尝试加载本地资源...');
        this.loadLocalXGPlayerResources();
      });
  }

  /**
   * 动态加载CSS文件
   */
  private loadCSS(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过该CSS
      const existingLink = document.querySelector(`link[href="${url}"]`);
      if (existingLink) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = url;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load CSS: ${url}`));
      document.head.appendChild(link);
    });
  }

  /**
   * 动态加载JS文件
   */
  private loadScript(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过该脚本
      const existingScript = document.querySelector(`script[src="${url}"]`);
      if (existingScript) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = url;
      script.charset = 'utf-8';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
      document.head.appendChild(script);
    });
  }

  /**
   * 加载本地xgplayer资源（备选方案）
   */
  private loadLocalXGPlayerResources() {
    // 尝试加载本地资源
    this.loadCSS('./libs/xgplayer/index.min.css')
      .then(() => {
        cc.log('本地XGPlayer CSS加载完成');
        return this.loadScript('./libs/xgplayer/index.min.js');
      })
      .then(() => {
        cc.log('本地XGPlayer JS加载完成');
        this.insertVideoWithMask();
      })
      .catch(error => {
        cc.error('加载本地XGPlayer资源也失败:', error);
        cc.error('无法加载XGPlayer，将使用原生video标签');
      });
  }

  /**
   * 观看视频按钮点击事件
   * 调用游戏场景管理器的增加时长方法
   */
  watchVideoClick() {
    // 获取游戏场景管理器的引用
    const gameSceneManager = this.node.parent.getComponent('gameSceneManager');
    if (gameSceneManager && gameSceneManager.addExtraTimeAfterVideo) {
      // 调用增加时长方法，默认增加30秒
      gameSceneManager.addExtraTimeAfterVideo(30);
      cc.log('观看视频按钮被点击，尝试增加30秒游戏时间');
    } else {
      cc.error('无法获取游戏场景管理器引用或方法不存在');
    }
  }
  /**
   * 手动插入video和mask - 对外调用的主方法
   */
  insertVideoWithMask() {
    // 使用xgplayer需要用到资源
    // <link rel="stylesheet" href="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.css">
    // <script src="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.js" charset="utf-8"></script>

    // 创建遮罩层
    this.createMask();

    // 创建video元素
    this.createVideo();

    // xgplayer已在初始化时设置好样式和事件监听
  }

  /**
   * 创建遮罩层
   */
  private createMask() {
    this.maskElement = document.createElement('div');
    this.maskElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9998;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    `;
    document.body.appendChild(this.maskElement);
  }

  /**
   * 创建video元素 - 使用xgplayer
   */
  private createVideo() {
    // 创建视频容器 - 在父元素上统一设置横屏旋转
    this.videoContainer = document.createElement('div');
    this.videoContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 30px;
      width: 100%;
      height: 100%;
      justify-content: center;
      transform: rotate(90deg);
      transform-origin: center center;
    `;

    // 创建xgplayer容器
    const playerContainer = document.createElement('div');
    playerContainer.id = 'xgplayer-container-' + Date.now();

    // 计算视频尺寸：高度为屏幕宽度的60%，宽度按16:9比例计算
    const videoSize = this.calculateVideoSize();
    playerContainer.style.cssText = `
      width: ${videoSize.width}px;
      height: ${videoSize.height}px;
    `;

    this.videoContainer.appendChild(playerContainer);

    this.scheduleOnce(() => {
      // 初始化xgplayer
      this.initXGPlayer(playerContainer.id, videoSize);
    }, 0.1);

    // 创建按钮容器
    this.createButtons(this.videoContainer);

    // 将视频容器添加到遮罩层
    this.maskElement.appendChild(this.videoContainer);
  }

  /**
   * 计算视频尺寸：高度为屏幕宽度的60%，保持16:9比例
   */
  private calculateVideoSize() {
    // 获取屏幕尺寸
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // 视频高度为屏幕宽度的60%（因为旋转后，屏幕宽度对应视频的高度方向）
    const videoHeight = screenWidth * 0.6;

    // 按16:9比例计算宽度
    const videoWidth = videoHeight * (16 / 9);

    // 确保不超过屏幕高度（旋转后对应宽度方向）
    const maxWidth = screenHeight * 0.8; // 留一些边距
    const finalWidth = Math.min(videoWidth, maxWidth);
    const finalHeight = finalWidth * (9 / 16);

    cc.log(`屏幕尺寸: ${screenWidth}x${screenHeight}`);
    cc.log(
      `计算的视频尺寸: ${Math.round(finalWidth)}x${Math.round(finalHeight)}`
    );

    return {
      width: Math.round(finalWidth),
      height: Math.round(finalHeight),
    };
  }

  /**
   * 添加屏幕尺寸变化监听
   */
  private addResizeListener() {
    this.resizeHandler = () => {
      // 如果播放器存在且正在播放，重新调整尺寸
      if (this.xgPlayer && this.videoContainer) {
        const newSize = this.calculateVideoSize();

        // 更新播放器容器尺寸
        const playerElement = document.getElementById(this.xgPlayer.config.id);
        if (playerElement) {
          playerElement.style.width = `${newSize.width}px`;
          playerElement.style.height = `${newSize.height}px`;
        }

        // 更新播放器配置
        if (this.xgPlayer.resize) {
          this.xgPlayer.resize(newSize.width, newSize.height);
        }

        cc.log(
          `屏幕尺寸变化，视频尺寸调整为: ${newSize.width}x${newSize.height}`
        );
      }
    };

    // 添加监听器
    window.addEventListener('resize', this.resizeHandler);
    window.addEventListener('orientationchange', this.resizeHandler);
  }

  /**
   * 移除屏幕尺寸变化监听
   */
  private removeResizeListener() {
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
      window.removeEventListener('orientationchange', this.resizeHandler);
      this.resizeHandler = null;
    }
  }

  /**
   * 初始化xgplayer播放器
   */
  private initXGPlayer(
    containerId: string,
    videoSize?: { width: number; height: number }
  ) {
    // 检查xgplayer是否已加载
    if (typeof window['Player'] === 'undefined') {
      cc.error('XGPlayer未加载，请检查静态资源引入');
      return;
    }

    try {
      // 使用计算出的尺寸，如果没有传入则使用默认值
      const size = videoSize || { width: 400, height: 225 };

      this.xgPlayer = new window['Player']({
        id: containerId,
        url: 'https://vod.jgrm.net/550126f54ec741ce35ecdb067bbb3537_raw.mp4',
        width: size.width,
        height: size.height,
        autoplay: true,
        // loop: true, // 循环播放不能触发end事件
        playsinline: true,
        controls: {
          mode: 'flex', // 显示控制条
          initShow: true, // 初始显示控制条
          autoHide: false, // 不自动隐藏
        },
        // 只保留播放按钮和进度条，移除其他控件
        ignores: [
          'play', // 控制栏上的播放/暂停控制插件
          'volume', // 移除音量控制
          'fullscreen', // 移除全屏按钮
          'pip', // 移除画中画
          'playbackRate', // 移除播放速度
          'definition', // 移除清晰度
          'download', // 移除下载
          'screenShot', // 移除截图
          'airplay', // 移除投屏
          'cssFullscreen', // 移除网页全屏
          'rotate', // 移除旋转
          'replay', // 移除重播
          'time', // 移除时间显示
        ],
        // 进度条配置
        progress: {
          isDraggingSeek: false, // 禁用拖拽跳转
          closeMoveSeek: true, // 是否关闭滑块seek能力
          isCloseClickSeek: true,
        },
        // poster: '', // 可以设置封面图
        // 移动端优化配置
        'webkit-playsinline': true,
        'x5-playsinline': true,
        'x5-video-player-type': 'h5-page',
      });

      // 设置播放器事件监听
      this.setupXGPlayerEvents();

      // 禁用暂停功能
      this.disablePauseFunction();
    } catch (error) {
      cc.error('初始化XGPlayer失败:', error);
    }
  }

  /**
   * 设置xgplayer事件监听
   */
  private setupXGPlayerEvents() {
    if (!this.xgPlayer) return;

    // 播放完成事件
    this.xgPlayer.on('ended', () => {
      cc.log('XGPlayer: 视频播放完成');
      this.onVideoCompleted();
    });

    // 播放开始事件
    this.xgPlayer.on('play', () => {
      cc.log('XGPlayer: 视频开始播放');
      this.musicNote.getComponent('MusicNote').pauseMusic();
    });

    // 暂停事件 - 立即恢复播放
    this.xgPlayer.on('pause', () => {
      cc.log('XGPlayer: 检测到暂停，立即恢复播放');
      // 延迟一帧恢复播放，避免无限循环
      setTimeout(() => {
        if (this.xgPlayer && !this.xgPlayer.ended) {
          this.xgPlayer.play();
        }
      }, 10);
    });

    // 加载完成事件
    this.xgPlayer.on('loadeddata', () => {
      cc.log('XGPlayer: 视频数据加载完成');
    });

    // 错误事件
    this.xgPlayer.on('error', (error: any) => {
      cc.error('XGPlayer: 播放错误', error);
    });
  }

  /**
   * 禁用暂停功能
   */
  private disablePauseFunction() {
    if (!this.xgPlayer) return;

    // 监听点击事件，阻止暂停
    this.xgPlayer.on('click', (event: any) => {
      // 阻止默认的点击暂停行为
      event.preventDefault && event.preventDefault();
      event.stopPropagation && event.stopPropagation();

      // 确保视频继续播放
      if (this.xgPlayer.paused && !this.xgPlayer.ended) {
        this.xgPlayer.play();
      }
    });

    // 监听键盘事件，阻止空格键暂停
    this.xgPlayer.on('keydown', (event: any) => {
      if (event.keyCode === 32) {
        // 空格键
        event.preventDefault && event.preventDefault();
        event.stopPropagation && event.stopPropagation();
      }
    });

    // 禁用右键菜单
    const playerElement = document.getElementById(this.xgPlayer.config.id);
    if (playerElement) {
      playerElement.addEventListener('contextmenu', e => {
        e.preventDefault();
      });
    }
  }

  /**
   * 创建按钮 - 显示在video下方（父元素已设置旋转）
   */
  private createButtons(container: HTMLElement) {
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: flex;
      gap: 20px;
    `;

    // 创建"观看完成"按钮 - 初始隐藏
    this.completeBtn = document.createElement('button');
    this.completeBtn.textContent = '观看完成';
    this.completeBtn.style.cssText = `
      padding: 5px 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: none;
    `;
    this.completeBtn.onclick = () => this.onCompleteButtonClick();

    // 创建"放弃"按钮 - 初始显示
    this.cancelBtn = document.createElement('button');
    this.cancelBtn.textContent = '放弃';
    this.cancelBtn.style.cssText = `
      padding: 5px 10px;
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: block;
    `;
    this.cancelBtn.onclick = () => this.onCancelButtonClick();

    buttonContainer.appendChild(this.completeBtn);
    buttonContainer.appendChild(this.cancelBtn);
    container.appendChild(buttonContainer);
  }

  /**
   * 视频播放完成回调
   */
  private onVideoCompleted() {
    cc.log('视频播放完成，显示观看完成按钮，隐藏放弃按钮');

    // 显示"观看完成"按钮
    if (this.completeBtn) {
      this.completeBtn.style.display = 'block';
    }

    // 隐藏"放弃"按钮
    if (this.cancelBtn) {
      this.cancelBtn.style.display = 'none';
    }
  }

  /**
   * 完成按钮点击事件
   */
  private onCompleteButtonClick() {
    cc.log('观看完成按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
    this.musicNote.getComponent('MusicNote').resumeMusic();
    this.watchVideoClick();
  }

  /**
   * 放弃按钮点击事件
   */
  private onCancelButtonClick() {
    cc.log('放弃按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
    this.scheduleOnce(() => {
      this.backToLevelSelect();
    }, 0.5);
    // this.musicNote.getComponent('MusicNote').resumeMusic();
  }

  /**
   * 关闭video和mask
   */
  closeVideoAndMask() {
    // 销毁xgplayer实例
    if (this.xgPlayer) {
      try {
        this.xgPlayer.destroy();
        cc.log('XGPlayer实例已销毁');
      } catch (error) {
        cc.error('销毁XGPlayer实例失败:', error);
      }
      this.xgPlayer = null;
    }

    // 移除遮罩层（包含video和按钮）
    if (this.maskElement) {
      this.maskElement.remove();
      this.maskElement = null;
    }

    // 重置容器和按钮引用
    this.videoContainer = null;
    this.completeBtn = null;
    this.cancelBtn = null;
    this.node.active = false;

    cc.log('video和mask已关闭');
  }

  /**
   * 开始播放视频 - 对外调用的方法
   */
  startVideo() {
    // 如果xgplayer已加载，直接播放
    if (typeof window['Player'] !== 'undefined') {
      this.insertVideoWithMask();
    } else {
      // 否则先加载资源
      this.loadXGPlayerResources();
    }
  }

  onDestroy() {
    // 清理资源，确保video和mask被正确移除
    this.closeVideoAndMask();

    // 移除屏幕尺寸变化监听
    this.removeResizeListener();
  }
}
