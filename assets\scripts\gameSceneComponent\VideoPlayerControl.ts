const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';

@ccclass
export default class VideoPlayerControl extends cc.Component {
  // 遮罩层和视频元素
  private maskElement: HTMLElement = null;
  private videoElement: HTMLVideoElement = null;

  // 按钮元素
  private completeBtn: HTMLButtonElement = null;
  private cancelBtn: HTMLButtonElement = null;

  // 保存原始宽高
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  onLoad() {
    this.insertVideoWithMask();
  }

  /**
   * 观看视频按钮点击事件
   * 调用游戏场景管理器的增加时长方法
   */
  watchVideoClick() {
    // 获取游戏场景管理器的引用
    const gameSceneManager = this.node.parent.getComponent('gameSceneManager');
    if (gameSceneManager && gameSceneManager.addExtraTimeAfterVideo) {
      // 调用增加时长方法，默认增加30秒
      gameSceneManager.addExtraTimeAfterVideo(30);
      cc.log('观看视频按钮被点击，尝试增加30秒游戏时间');
    } else {
      cc.error('无法获取游戏场景管理器引用或方法不存在');
    }
  }
  /**
   * 手动插入video和mask - 对外调用的主方法
   */
  insertVideoWithMask() {
    // 使用xgplayer需要用到资源
    // <link rel="stylesheet" href="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.css">
    // <script src="https://unpkg.byted-static.com/xgplayer/3.0.21/dist/index.min.js" charset="utf-8"></script>
    // <script src="https://unpkg.byted-static.com/xgplayer-mp4/3.0.21/dist/index.min.js" charset="utf-8"></script>

    // 创建遮罩层
    this.createMask();

    // 创建video元素
    this.createVideo();

    // 设置video样式为横屏居中
    this.setupVideoStyle();

    // 监听video播放完成事件
    this.setupVideoEvents();
  }

  /**
   * 创建遮罩层
   */
  private createMask() {
    this.maskElement = document.createElement('div');
    this.maskElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9998;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    `;
    document.body.appendChild(this.maskElement);
  }

  /**
   * 创建video元素
   */
  private createVideo() {
    // 创建视频容器 - 在父元素上统一设置横屏旋转
    const videoContainer = document.createElement('div');
    videoContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 30px;
      width: 100%;
      height: 100%;
      justify-content: center;
      transform: rotate(90deg);
      transform-origin: center center;
    `;

    // 创建视频元素,
    let videoStr = `
      <video class="cocosvideos" preload="auto" controls webkit-playsinline="" autoplay playsinline="true" webkit-playsinline="true" x5-playsinline="true" x5-video-player-type="h5-page" tabindex="2" mediatype="video" width="400" height="225">
        <source src="https://vod.jgrm.net/550126f54ec741ce35ecdb067bbb3537_raw.mp4">
      </video>
    `;

    videoContainer.insertAdjacentHTML('beforeend', videoStr);
    this.videoElement = videoContainer.querySelector(
      'video'
    ) as HTMLVideoElement;

    // 创建按钮容器
    this.createButtons(videoContainer);

    // 将视频容器添加到遮罩层
    this.maskElement.appendChild(videoContainer);
  }

  /**
   * 创建按钮 - 显示在video下方（父元素已设置旋转）
   */
  private createButtons(container: HTMLElement) {
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: flex;
      gap: 20px;
    `;

    // 创建"观看完成"按钮 - 初始隐藏
    this.completeBtn = document.createElement('button');
    this.completeBtn.textContent = '观看完成';
    this.completeBtn.style.cssText = `
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: none;
    `;
    this.completeBtn.onclick = () => this.onCompleteButtonClick();

    // 创建"放弃"按钮 - 初始显示
    this.cancelBtn = document.createElement('button');
    this.cancelBtn.textContent = '放弃';
    this.cancelBtn.style.cssText = `
      padding: 10px 20px;
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      min-width: 100px;
      white-space: nowrap;
      display: block;
    `;
    this.cancelBtn.onclick = () => this.onCancelButtonClick();

    buttonContainer.appendChild(this.completeBtn);
    buttonContainer.appendChild(this.cancelBtn);
    container.appendChild(buttonContainer);
  }

  /**
   * 设置video样式 - 16:9比例，为按钮留出空间（父元素已设置旋转）
   */
  private setupVideoStyle() {
    if (!this.videoElement) return;

    // 去掉 Cocos 自动加的矩阵
    this.videoElement.style.transform = 'none';

    // 设置基础样式
    this.videoElement.style.position = 'relative';
    this.videoElement.style.zIndex = '9999';

    // 设置16:9比例尺寸 - 缩小尺寸为按钮留空间
    const videoWidth = 400; // 宽度，缩小以留出按钮空间
    const videoHeight = 225; // 16:9比例对应的高度 (400 * 9 / 16 = 225)

    this.videoElement.style.width = `${videoWidth}px`;
    this.videoElement.style.height = `${videoHeight}px`;

    // 给videoElement添加width、height属性
    this.videoElement.width = videoWidth;
    this.videoElement.height = videoHeight;
    // 不需要单独设置旋转，父元素已统一设置
  }

  /**
   * 设置video事件监听
   */
  private setupVideoEvents() {
    if (!this.videoElement) return;

    // 监听视频播放完成事件
    this.videoElement.addEventListener('ended', () => {
      cc.log('视频播放完成');
      this.onVideoCompleted();
    });

    // 监听视频加载完成事件
    this.videoElement.addEventListener('loadeddata', () => {
      cc.log('视频数据加载完成');
    });

    // 监听视频播放开始事件
    this.videoElement.addEventListener('play', () => {
      cc.log('视频开始播放');
    });

    // 监听视频暂停事件
    this.videoElement.addEventListener('pause', () => {
      cc.log('视频暂停');
    });
  }

  /**
   * 视频播放完成回调
   */
  private onVideoCompleted() {
    cc.log('视频播放完成，显示观看完成按钮，隐藏放弃按钮');

    // 显示"观看完成"按钮
    if (this.completeBtn) {
      this.completeBtn.style.display = 'block';
    }

    // 隐藏"放弃"按钮
    if (this.cancelBtn) {
      this.cancelBtn.style.display = 'none';
    }
  }

  /**
   * 完成按钮点击事件
   */
  private onCompleteButtonClick() {
    cc.log('观看完成按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 放弃按钮点击事件
   */
  private onCancelButtonClick() {
    cc.log('放弃按钮被点击，关闭video和mask');
    this.closeVideoAndMask();
  }

  /**
   * 关闭video和mask
   */
  closeVideoAndMask() {
    // 移除video元素
    if (this.videoElement) {
      this.videoElement.pause();
      this.videoElement = null;
    }

    // 移除遮罩层（包含video和按钮）
    if (this.maskElement) {
      this.maskElement.remove();
      this.maskElement = null;
    }

    // 重置按钮引用
    this.completeBtn = null;
    this.cancelBtn = null;

    cc.log('video和mask已关闭');
  }

  /**
   * 开始播放视频 - 对外调用的方法
   */
  startVideo() {
    this.insertVideoWithMask();
  }

  onDestroy() {
    // 清理资源，确保video和mask被正确移除
    this.closeVideoAndMask();
  }
}
